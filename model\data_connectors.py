import turbodbc
import logging

class SQLServerDatabaseConnection:
    '''
      This class uses turbodbc to connect to the database and execute queries. 
      Also implements a context manager and a logger that logs the queries executed and errors if any.
      
    '''

    def __init__(self, server, database, username, password):
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.options = turbodbc.make_options( use_async_io=True, prefer_unicode=True, autocommit=True )         
        self.connection = turbodbc.connect(driver="ODBC Driver 17 for SQL Server", server= self.server, database= self.database, uid= self.username, pwd= self.password, turbodbc_options = self.options )
        self.cursor = self.connection.cursor()  
        self.logger = logging.getLogger(__name__)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if exc_type is not None:
            self.logger.error(f'Error: {exc_type} - {exc_value}')
             
        self.cursor.close()
        self.connection.close() 
        self.logger.info('Connection closed')   

    def execute_query(self, query):     
        self.cursor.execute(query)
        self.logger.info(f'Query executed: {query}')
        return self.cursor.fetchallarrow().to_pandas()
    
    def execute_query_with_params(self, query, params):
        self.cursor.execute(query, params)
        self.logger.info(f'Query executed: {query} with params {params}')
        return self.cursor.fetchall()

    
