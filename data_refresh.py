from excel.excel_integration import ExcelIntegration
import yaml
import json
from model.data_connectors import SQLServerDatabaseConnection

if __name__ == '__main__':
    with open('./config/config.json') as f:
        config = json.load(f)

    # Load the sql queries file
    with open('./config/sql_queries.yml') as f:
        sql_queries = yaml.load(f, Loader=yaml.FullLoader)

    # The following code executes the query and stores the results in a dictionary
    df = {}
    query_name = 'product_dims_and_load_qty'
    query_string = sql_queries[query_name]['query']
    database_to_hit = sql_queries[query_name]['database']
    with SQLServerDatabaseConnection( **config['databases'][database_to_hit], **config['database_users']['dbuser'] ) as conn:
        result = conn.execute_query(query_string)
        df[f'{query_name}'] = result

    input_data_map = df['product_dims_and_load_qty'].loc[:, ['desc', 'itemid', 'factory', 'planner'] ].sort_values(by=['planner', 'factory', 'desc'])
    print(input_data_map)
    ExcelIntegration.populate_workbook('./PO_Optimization.xlsm', input_data_map)
