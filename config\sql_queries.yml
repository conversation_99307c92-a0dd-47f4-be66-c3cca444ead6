# This query fetches the latest 12 weeks of data from the nh_forecastdata table for all items
forecast_data: 
    database: NaomiHome
    query: |
        with latest_manual_forecast_data as (
        select *, ROW_NUMBER() OVER( partition by itemID order by Week ) WeekOrder from NaomiHome.dbo.nh_forecastdata nf 
        where nf.forecastdate = ( select max(forecastdate) from NaomiHome.dbo.nh_forecastdata where type = 'Manual' )
        )

        select * from latest_manual_forecast_data 
        where WeekOrder <= 51
        order by ItemID, Week

# This query fetches the list of week names for the next 12 weeks from the nh_forecastdata table
weeks_index:
    database: NaomiHome
    query: | 
        with latest_manual_forecast_data as (
        select *, ROW_NUMBER() OVER( partition by itemID order by Week ) WeekOrder from NaomiHome.dbo.nh_forecastdata nf 
        where nf.forecastdate = ( select max(forecastdate) from NaomiHome.dbo.nh_forecastdata where type = 'Manual' )
        )

        select distinct Week from latest_manual_forecast_data 
        where WeekOrder <= 51
        order by Week

# This query fetches the current inventory and gross margins for all Naomi Home products
curr_inv_and_gross_margins: 
    database: OJCommerce
    query: |
        with inv as ( 
        select itemid, sum(qty) curr_inventory, max(updated) LastUpdated from product_details_warehouse_inventory pdwi  
        join product_details pd on pd.id = pdwi.itemid
        join product p on pd.pid = p.id 
        join manufacturer m on m.id = p.manufacturer_id
        where m.name = 'Naomi Home' and pd.availability <= 2 
        group by itemid
        ),

        gm as (
        select pd.id itemid, pd.standard_cost, pd.calculated_shipping_cost, pd.price, ( pd.price  - ( pd.standard_cost + pd.calculated_shipping_cost ) ) gross_margin
        from product_details pd 
        join product p on pd.pid = p.id 
        join manufacturer m on m.id = p.manufacturer_id
        where m.name = 'Naomi Home' and pd.availability <= 2 
        )

        select gm.*, inv.curr_inventory, inv.LastUpdated last_updated from gm 
        left join inv on gm.itemid = inv.itemid 
        order by curr_inventory desc

# This query fetches the product dimensions and load quantity from the tbl_nh_items_po_planning table
product_dims_and_load_qty:
    database: NaomiHome
    query: |
        select * from tbl_nh_items_po_planning where not 
        ( sku is null or factory is null or planner is null or [length]  is null or width is null or height is null or cuft is null)
        and itemid in ( select distinct itemID from NaomiHome.dbo.nh_forecastdata where forecastdate = ( select max(forecastdate) from NaomiHome.dbo.nh_forecastdata where type = 'Manual' ) )
        order by [unique identifier - for container product mix]

#The following query fetches the arrival data for the next 12 weeks for all items
arrival_data_modifiable:
    database: OJCommerce
    query: |
        with item_arrivals_by_warehouse as ( 

            select
                w.name as warehouse,
                poi.itemid,
                po.ref_number,
                cast(po.po_expected_date as DATE) as expectedDate,
                poi.qty as PO_qty,
                CONCAT( DATEPART(yyyy,po.po_expected_date) ,'-' , DATEPART(ww,po.po_expected_date) ) WeekYr,
                po.ltl_waybill_ref as container#,
                case
                    when po.ltl_waybill_ref is null or po.ltl_waybill_ref = '' then 'modifiable' else 'unmodifiable'
                end as modstatus
            from
                purchase_order (nolock) po
            join purchase_order_items (nolock) poi on po.id = poi.poid
            left join purchase_order_items_receiving (nolock) poir on poir.poiid = poi.id
            join product_details (nolock) pd on pd.id = poi.itemid
            join product (nolock) p on p.id = pd.pid
            join warehouse (nolock) w on w.id = po.po_warehouse_id
            where
                p.manufacturer_id = 3567
                and po.po_type = 'WAREHOUSE'
                and poi.qty is not null
                and po.po_expected_date is not null
                and (po.status is null 	or po.status not in ('CLOSED', 'CANCELLED', 'HOLD'))
                and w.name in ('ARMS Logistics', 'FCI')
            )


        select itemid, WeekYr, sum(PO_qty) total_arrival_qty 
        from item_arrivals_by_warehouse
        where modstatus = 'modifiable'
        group by itemid, WeekYr
        order by 2

arrival_data_unmodifiable:
    database: OJCommerce
    query: |
        with item_arrivals_by_warehouse as ( 

            select
                w.name as warehouse,
                poi.itemid,
                po.ref_number,
                cast(po.po_expected_date as DATE) as expectedDate,
                poi.qty as PO_qty,
                CONCAT( DATEPART(yyyy,po.po_expected_date) ,'-' , DATEPART(ww,po.po_expected_date) ) WeekYr,
                po.ltl_waybill_ref as container#,
                case
                    when po.ltl_waybill_ref is null or po.ltl_waybill_ref = '' then 'modifiable' else 'unmodifiable'
                end as modstatus
            from
                purchase_order (nolock) po
            join purchase_order_items (nolock) poi on po.id = poi.poid
            left join purchase_order_items_receiving (nolock) poir on poir.poiid = poi.id
            join product_details (nolock) pd on pd.id = poi.itemid
            join product (nolock) p on p.id = pd.pid
            join warehouse (nolock) w on w.id = po.po_warehouse_id
            where
                p.manufacturer_id = 3567
                and po.po_type = 'WAREHOUSE'
                and poi.qty is not null
                and po.po_expected_date is not null
                and (po.status is null 	or po.status not in ('CLOSED', 'CANCELLED', 'HOLD'))
                and w.name in ('ARMS Logistics', 'FCI')
            )


        select itemid, WeekYr, sum(PO_qty) total_arrival_qty 
        from item_arrivals_by_warehouse
        where modstatus = 'unmodifiable'
        group by itemid, WeekYr
        order by 2