# PO Optimization Project - Installation Guide

This document provides instructions for setting up the dependencies for the PO Optimization project.

## Prerequisites

- Anaconda or Miniconda installed
- Python 3.9 or higher
- Microsoft Excel (for xlwings functionality)
- SQL Server ODBC Driver 17 (for database connectivity)

## Installation Options

### Option 1: Using Conda Environment File (Recommended)

1. Create and activate the conda environment:
```bash
conda env create -f environment.yml
conda activate po-optimization
```

### Option 2: Using pip with requirements.txt

If you prefer to use pip or already have a conda environment:

1. Activate your conda environment:
```bash
conda activate your-environment-name
```

2. Install the additional packages:
```bash
pip install -r requirements.txt
```

### Option 3: Manual Installation

Install each package individually:

```bash
# Excel integration
pip install xlwings>=0.30.0

# Database connectivity
pip install turbodbc>=4.5.0
pip install pyodbc>=4.0.0

# Optimization modeling
conda install -c conda-forge pyomo>=6.6.0

# Optimization solver
pip install highspy>=1.5.0

# Data validation
pip install pydantic>=2.0.0

# GUI components
pip install tkcalendar>=1.6.0
```

## Additional Setup

### xlwings Setup
After installing xlwings, you may need to install the Excel add-in:
```bash
xlwings addin install
```

### Database Drivers
Ensure you have the Microsoft ODBC Driver 17 for SQL Server installed:
- Download from: https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server

### Solver for Pyomo (Optional)
For optimization problems, you may want to install a solver:
```bash
# For open-source solvers
conda install -c conda-forge glpk
conda install -c conda-forge ipopt
conda install -c conda-forge cbc

# Or for commercial solvers (requires license)
# CPLEX, Gurobi, etc.
```

## Verification

To verify the installation, run:
```python
import xlwings
import pandas as pd
import pyomo.environ as pyo
import pydantic
import turbodbc
import pyodbc
import tkcalendar
import highspy
print("All packages imported successfully!")
```

## Package Descriptions

- **xlwings**: Excel integration for Python
- **turbodbc**: High-performance ODBC database connectivity
- **pyodbc**: Python ODBC database connectivity
- **pyomo**: Optimization modeling framework
- **highspy**: HiGHS linear programming solver for Python
- **pydantic**: Data validation using Python type annotations
- **tkcalendar**: Calendar widget for tkinter GUI applications

## Troubleshooting

### Common Issues:

1. **xlwings not working**: Ensure Excel is installed and the xlwings add-in is properly installed
2. **Database connection issues**: Verify ODBC drivers are installed and connection strings are correct
3. **Pyomo solver issues**: Install appropriate solvers for your optimization problems

### Getting Help:

- Check the official documentation for each package
- Ensure all prerequisites are met
- Verify Python and package versions are compatible
