name: po-optimization
channels:
  - conda-forge
  - defaults
dependencies:
  # Base Python and standard packages (included in Anaconda)
  - python>=3.9
  - pandas
  - numpy
  - matplotlib
  - seaborn
  - jupyter
  - ipython
  - openpyxl
  - xlrd
  - requests
  - beautifulsoup4
  - lxml
  - sqlalchemy
  - psycopg2
  - pymongo
  - scikit-learn
  - scipy
  - statsmodels
  - plotly
  - bokeh
  - dask
  - numba
  - h5py
  - pytables
  - bottleneck
  - numexpr
  - patsy
  - cloudpickle
  - fsspec
  - packaging
  - setuptools
  - wheel
  - pip

  # Additional packages not in standard Anaconda
  - xlwings>=0.30.0
  - pydantic>=2.0.0
  - pyomo>=6.6.0
  - pyyaml>=6.0

  # Packages that might need pip installation
  - pip:
    - turbodbc>=4.5.0
    - pyodbc>=4.0.0
    - highspy>=1.5.0
    - tkcalendar>=1.6.0
